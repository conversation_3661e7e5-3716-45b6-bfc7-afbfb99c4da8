#!/usr/bin/env python3
"""
TradeExecutor - Real-time trading module with identical logic to training environment.
Integrates all the corrected SL/TP handling, TSL, daily risk management, and Binance API calls.
"""

import time
import logging
from datetime import datetime, timezone
from typing import Dict, Any, Tuple, Optional
import numpy as np
import pandas as pd
from binance.client import Client as BinanceClient
from binance.exceptions import BinanceAPIException

log = logging.getLogger("TradeExecutor")

class TradeExecutor:
    """
    Real-time trade execution with corrected environment logic.
    Handles position management, SL/TP, TSL, daily risk management, and Binance API integration.
    """
    
    def __init__(self, config: Dict[str, Any], test_mode: bool = False):
        """Initialize TradeExecutor with configuration."""
        self.config = config
        self.test_mode = test_mode
        
        # Account settings
        account_cfg = config.get('account', {})
        self.initial_equity = account_cfg.get('initialEquity', 10000)
        self.equity_total = self.initial_equity
        self.max_equity = self.initial_equity
        
        # Trade parameters (EXACTLY as in simulate_trading.py)
        trade_params = config.get('tradeParams', {})
        self.fee_perc = trade_params.get('feePercentage', 0.0) / 100.0
        self.slippage_perc = trade_params.get('slippagePercentage', 0.0) / 100.0
        self.min_sl_dist_perc = trade_params.get('minSLDistancePercent', 0.0) / 100.0
        self.min_sl_dist_atr_mult = trade_params.get('minSLDistanceATR', 1.0)
        self.min_atr_perc_price_threshold = trade_params.get('minAtrPercentOfPrice', 0.0) / 100.0
        self.rr_target = trade_params.get('rrTarget', 2.0)  # CRITICAL PARAMETER
        
        # Asymmetric entry thresholds (as in env)
        entry_thr = trade_params.get('entryActionThreshold', 0.5)
        self.long_entry_thr = trade_params.get('longEntryThreshold', entry_thr)
        self.short_entry_thr = trade_params.get('shortEntryThreshold', entry_thr)
        self.exit_thr = trade_params.get('exitActionThreshold', 0.4)
        
        # Agent exit control
        self.agent_exits_enabled = trade_params.get('agentExitsEnabled', True)
        
        # Risk management
        risk_mgmt = config.get('riskManagement', {})
        self.position_sizing_method = risk_mgmt.get('positionSizingMethod', 'RiskPercentage')
        self.risk_per_trade = risk_mgmt.get('riskPerTradePercentage', 0.0) / 100.0
        self.max_pos_perc_equity = risk_mgmt.get('maxPositionSizePercentEquity', 100.0) / 100.0
        
        # Trailing Stop Loss (EXACTLY as in ScalpingEnv)
        tsl_config = config.get('trailingStopLoss', {})
        self.tsl_enabled = tsl_config.get('enabled', False)
        self.tsl_activate_atr_mult = tsl_config.get('activateATRMultiplier', 1.0)
        self.tsl_trail_atr_mult = tsl_config.get('trailATRMultiplier', 0.8)
        
        # Time-of-day blackout hours (as in ScalpingEnv)
        self.DISALLOWED_HOURS = {0, 1, 2, 3}  # 00:00-03:59 UTC
        
        # Current position state
        self.position = 0  # -1, 0, 1
        self.position_size = 0.0
        self.entry_price = 0.0
        self.entry_time = None
        self.entry_fee = 0.0
        self.current_sl_price = 0.0
        self.original_sl_price = 0.0  # Store original SL for R-multiple calculation
        self.current_tp_price = 0.0
        self.peak_price_in_trade = 0.0
        self.trailing_active = False
        self.entry_atr = 0.0
        self.open_idx_sim = -1
        
        # Daily risk management (EXACTLY as in ScalpingEnv)
        self.daily_pnl = 0.0
        self.daily_risk_used = 0.0
        self.daily_trade_count = 0
        self.last_day = None
        self.trading_allowed = True
        
        # Cooldown management
        self.cooldown_counter = 0
        self.last_update_time = time.time()
        
        # Trading history
        self.trades = []
        
        # Action stability tracking for 1s decisions
        self.recent_actions = []
        self.action_stability_window = 3  # Require 3 consistent signals
        self.action_stability_threshold = 0.5  # Max deviation allowed (increased from 0.3)

        # Signal smoothing with EMA
        self.action_ema = None
        self.ema_alpha = 0.3  # EMA smoothing factor (0.1-0.5 range)

        # Decision throttling
        self.last_decision_time = 0
        self.decision_interval = 5  # seconds between entry decisions
        
        log.info(f"TradeExecutor initialized:")
        log.info(f"  Entry thresholds - Long: {self.long_entry_thr}, Short: {self.short_entry_thr}, Exit: {self.exit_thr}")
        log.info(f"  Risk-Reward Target: {self.rr_target}")
        log.info(f"  Agent Exits Enabled: {self.agent_exits_enabled}")
        log.info(f"  TSL Enabled: {self.tsl_enabled} (Activate: {self.tsl_activate_atr_mult}R, Trail: {self.tsl_trail_atr_mult}x ATR)")
        log.info(f"  Initial Equity: ${self.equity_total:.2f}")
    
    def get_current_position(self) -> Tuple[int, float, float]:
        """
        Returns current position info.
        Returns: (position, position_size, entry_price)
        """
        return self.position, self.position_size, self.entry_price
    
    def update_equity(self, current_price: float) -> None:
        """Update equity with unrealized P&L."""
        if self.position != 0:
            unrealized_pnl = self.position_size * (current_price - self.entry_price) * self.position
            current_equity = self.equity_total + unrealized_pnl
            self.max_equity = max(self.max_equity, current_equity)
    
    def check_daily_reset(self, current_time: datetime) -> None:
        """Check and reset daily risk management limits."""
        cur_day = current_time.date()
        if self.last_day is None or cur_day != self.last_day:
            self.daily_pnl = 0.0
            self.daily_risk_used = 0.0
            self.daily_trade_count = 0
            self.trading_allowed = True
            self.last_day = cur_day
            log.info(f"{current_time}: New day, reset daily limits")
    
    def force_reset_daily_limits(self) -> None:
        """Manual reset of daily limits for testing purposes."""
        self.daily_pnl = 0.0
        self.daily_risk_used = 0.0
        self.daily_trade_count = 0
        self.trading_allowed = True
        log.warning("🔄 MANUAL RESET: Daily limits forcefully reset for testing")
    
    def check_daily_limits(self, current_time: datetime, current_atr: float) -> bool:
        """
        Check if daily limits are exceeded (EXACTLY as in ScalpingEnv).
        Returns True if trading should be stopped.
        """
        if (self.daily_risk_used <= -5.0 or
            self.daily_pnl <= -10 * current_atr or
            self.daily_trade_count >= 300) and self.trading_allowed:
            self.trading_allowed = False
            log.warning(f"{current_time}: Daily limit hit - risk: {self.daily_risk_used:.2f}, pnl: {self.daily_pnl:.2f}, trades: {self.daily_trade_count}")
            return True
        return False
    
    def update_cooldown(self) -> None:
        """Update cooldown counter based on elapsed time."""
        current_time = time.time()
        elapsed = current_time - self.last_update_time
        self.last_update_time = current_time
        
        if self.cooldown_counter > 0:
            self.cooldown_counter = max(0, self.cooldown_counter - int(elapsed))
    
    def check_trailing_stop(self, current_price: float, current_atr: float) -> Optional[str]:
        """
        Check and update trailing stop loss (FIXED VERSION from simulate_trading_new.py).
        Returns exit reason if TSL is triggered, None otherwise.
        """
        if self.position == 0 or not self.tsl_enabled or current_atr <= 0:
            return None
        
        if self.position == 1:  # Long position
            # Calculate R-multiple using ORIGINAL SL distance for accurate risk assessment
            original_sl_distance = abs(self.entry_price - self.original_sl_price) if hasattr(self, 'original_sl_price') and self.original_sl_price > 0 else abs(self.entry_price - self.current_sl_price)

            profit = current_price - self.entry_price
            r_mult = profit / original_sl_distance if original_sl_distance > 1e-9 else 0

            # DEBUG: Log R-multiple progress with original risk
            current_time = datetime.now(timezone.utc)
            log.info(f"🔍 TSL LONG DEBUG @ {current_time}: R={r_mult:.3f}/{self.tsl_activate_atr_mult:.1f} | Profit={profit:.5f} | Risk={original_sl_distance:.5f} | Price={current_price:.5f} | Entry={self.entry_price:.5f}")

            # Activate trailing when profit reaches tsl_activate_atr_mult R
            if not self.trailing_active and r_mult >= self.tsl_activate_atr_mult:
                self.trailing_active = True
                new_tsl_sl = current_price - self.tsl_trail_atr_mult * current_atr
                old_sl = self.current_sl_price

                # For TSL, allow SL to move much closer to entry but maintain reasonable minimum
                # Use 0.05% of entry price as minimum distance (more reasonable than 0.001 fixed)
                min_sl_distance = self.entry_price * 0.0005  # 0.05% of entry price
                min_allowed_sl = self.entry_price - min_sl_distance

                # Set new SL, but don't go above the minimum allowed level
                self.current_sl_price = max(new_tsl_sl, min_allowed_sl)
                self.peak_price_in_trade = current_price
                log.info(f"🎯 TSL LONG ACTIVATED @ {current_time}: R={r_mult:.2f} | Peak={current_price:.5f} | SL: {old_sl:.5f} → {self.current_sl_price:.5f} | ATR={current_atr:.5f}")
            elif self.trailing_active:
                # Update peak first - CRITICAL FIX
                if current_price > self.peak_price_in_trade:
                    self.peak_price_in_trade = current_price
                    # Only calculate new SL based on improved peak
                    new_sl = self.peak_price_in_trade - self.tsl_trail_atr_mult * current_atr

                    # Use same minimum distance logic as activation
                    min_sl_distance = self.entry_price * 0.0005  # 0.05% of entry price
                    min_allowed_sl = self.entry_price - min_sl_distance

                    # Only update if new SL is higher (tighter) than current
                    if new_sl > self.current_sl_price:
                        old_sl = self.current_sl_price
                        # Ensure we don't go above minimum allowed level
                        self.current_sl_price = max(new_sl, min_allowed_sl)
                        sl_movement = self.current_sl_price - old_sl
                        log.info(f"📈 TSL LONG MOVED @ {current_time}: Peak={self.peak_price_in_trade:.5f} | SL: {old_sl:.5f} → {self.current_sl_price:.5f} | Move=+{sl_movement:.5f}")

            # Check if SL is hit
            if self.current_sl_price > 0 and current_price <= self.current_sl_price:
                return "SL"
        
        elif self.position == -1:  # Short position
            # Calculate R-multiple using ORIGINAL SL distance for accurate risk assessment
            original_sl_distance = abs(self.entry_price - self.original_sl_price) if hasattr(self, 'original_sl_price') and self.original_sl_price > 0 else abs(self.entry_price - self.current_sl_price)

            profit = self.entry_price - current_price
            r_mult = profit / original_sl_distance if original_sl_distance > 1e-9 else 0

            # DEBUG: Log R-multiple progress with original risk
            current_time = datetime.now(timezone.utc)
            log.info(f"🔍 TSL SHORT DEBUG @ {current_time}: R={r_mult:.3f}/{self.tsl_activate_atr_mult:.1f} | Profit={profit:.5f} | Risk={original_sl_distance:.5f} | Price={current_price:.5f} | Entry={self.entry_price:.5f}")

            # Activate trailing when profit reaches tsl_activate_atr_mult R
            if not self.trailing_active and r_mult >= self.tsl_activate_atr_mult:
                self.trailing_active = True
                self.peak_price_in_trade = current_price  # Track lowest price reached
                old_sl = self.current_sl_price

                # For SHORT TSL, allow SL to move closer to entry but maintain reasonable minimum
                # Use 0.05% of entry price as minimum distance above entry
                min_sl_distance = self.entry_price * 0.0005  # 0.05% of entry price
                max_allowed_sl = self.entry_price + min_sl_distance

                new_tsl_sl = self.peak_price_in_trade + self.tsl_trail_atr_mult * current_atr
                # Set new SL, but don't go below the maximum allowed level
                self.current_sl_price = min(new_tsl_sl, max_allowed_sl)
                log.info(f"🎯 TSL SHORT ACTIVATED @ {current_time}: R={r_mult:.2f} | Peak={current_price:.5f} | SL: {old_sl:.5f} → {self.current_sl_price:.5f} | ATR={current_atr:.5f}")
            elif self.trailing_active:
                # Update peak (lowest price for SHORT)
                if current_price < self.peak_price_in_trade:
                    self.peak_price_in_trade = current_price
                    new_sl = self.peak_price_in_trade + self.tsl_trail_atr_mult * current_atr

                    # Use same minimum distance logic as activation
                    min_sl_distance = self.entry_price * 0.0005  # 0.05% of entry price
                    max_allowed_sl = self.entry_price + min_sl_distance

                    # Only move SL down (tighter) for SHORT, never up (looser)
                    if new_sl < self.current_sl_price:
                        old_sl = self.current_sl_price
                        # Ensure we don't go below maximum allowed level
                        self.current_sl_price = min(new_sl, max_allowed_sl)
                        sl_movement = old_sl - self.current_sl_price
                        log.info(f"📉 TSL SHORT MOVED @ {current_time}: Peak={self.peak_price_in_trade:.5f} | SL: {old_sl:.5f} → {self.current_sl_price:.5f} | Move=-{sl_movement:.5f}")

            # Check if SL is hit
            if self.current_sl_price > 0 and current_price >= self.current_sl_price:
                return "SL"
        
        return None
    
    def check_soft_breakeven(self, current_price: float) -> None:
        """Check and apply soft breakeven logic."""
        # This can be implemented if needed for additional risk management
        pass
    
    def check_sl_tp_triggers(self, current_price: float) -> Tuple[Optional[str], float]:
        """
        Check if SL or TP is triggered (EXACTLY as in ScalpingEnv).
        SL has priority over TP when both trigger.
        Returns: (exit_reason, exit_price)
        """
        if self.position == 0:
            return None, 0.0
        
        if self.position == 1:  # Long
            hit_sl = self.current_sl_price > 0 and current_price <= self.current_sl_price
            hit_tp = self.current_tp_price > 0 and current_price >= self.current_tp_price
            # SL has priority over TP (exactly like in env)
            if hit_sl and hit_tp:
                hit_tp = False
            
            if hit_sl:
                return "SL", self.current_sl_price
            elif hit_tp:
                return "TP", self.current_tp_price
                
        elif self.position == -1:  # Short
            hit_sl = self.current_sl_price > 0 and current_price >= self.current_sl_price
            hit_tp = self.current_tp_price > 0 and current_price <= self.current_tp_price
            # SL has priority over TP (exactly like in env)
            if hit_sl and hit_tp:
                hit_tp = False
            
            if hit_sl:
                return "SL", self.current_sl_price
            elif hit_tp:
                return "TP", self.current_tp_price
        
        return None, 0.0
    
    def calculate_position_size(self, entry_price: float, sl_distance: float, signal_strength: float) -> float:
        """
        Calculate position size using risk percentage method with signal strength scaling.
        EXACTLY as in simulate_trading.py
        """
        if self.position_sizing_method == 'RiskPercentage':
            risk_amount = self.equity_total * self.risk_per_trade
            size_in_units = risk_amount / sl_distance if sl_distance > 1e-9 else 0
        else:
            log.error(f"Unknown sizing method: {self.position_sizing_method}")
            return 0.0
        
        # Signal strength-based position sizing (as in ScalpingEnv)
        size_multiplier = 0.5 + 0.5 * min(signal_strength / 1.0, 1.0)  # Scale from 0.5x to 1.0x
        size_in_units *= size_multiplier
        
        # Apply max position size constraint
        max_size_by_equity = (self.equity_total * self.max_pos_perc_equity) / entry_price if entry_price > 0 else 0
        if max_size_by_equity > 0:
            size_in_units = min(size_in_units, max_size_by_equity)
        
        return size_in_units
    
    def format_quantity(self, symbol: str, quantity: float) -> float:
        """
        Format quantity according to Binance symbol precision requirements.
        For futures trading, most symbols require specific decimal precision.
        """
        # Common precision rules for major futures symbols
        precision_map = {
            'XRPUSDT': 0,  # Whole numbers only
            'XRPUSDC': 0,  # Whole numbers only
            'BTCUSDT': 3,  # 3 decimal places
            'ETHUSDT': 2,  # 2 decimal places
            'ADAUSDT': 0,  # Whole numbers only
            'SOLUSDT': 1,  # 1 decimal place
        }
        
        # Get precision for symbol, default to 0 if unknown
        precision = precision_map.get(symbol, 0)
        
        # Round to appropriate precision
        formatted_qty = round(quantity, precision)
        
        # Ensure minimum quantity of 1 for whole number symbols
        if precision == 0 and formatted_qty < 1:
            formatted_qty = 1
            
        log.debug(f"Formatted quantity {quantity:.6f} -> {formatted_qty} (precision: {precision})")
        return formatted_qty

    def place_market_order(self, binance_client: BinanceClient, symbol: str, side: str, quantity: float) -> Dict[str, Any]:
        """
        Place market order via Binance Futures API or simulate in test mode.
        Returns order result or None if failed.
        """
        try:
            # Format quantity according to Binance symbol precision requirements
            formatted_qty = self.format_quantity(symbol, quantity)
            log.info(f"Placing {side} FUTURES market order: {quantity:.6f} -> {formatted_qty} {symbol}")
            
            if self.test_mode:
                # Simulate successful futures order in test mode
                import random
                order_id = random.randint(100000, 999999)
                # Simulate slight price slippage
                price_slippage = 0.0001 * (1 if side == 'BUY' else -1)
                current_price = 2.1370  # Use current price from test
                fill_price = current_price + price_slippage
                
                simulated_order = {
                    'orderId': order_id,
                    'status': 'FILLED',
                    'symbol': symbol,
                    'side': side,
                    'type': 'MARKET',
                    'executedQty': str(formatted_qty),
                    'fills': [{
                        'price': str(fill_price),
                        'qty': str(formatted_qty),
                        'commission': str(formatted_qty * fill_price * 0.0004),  # Futures fee 0.04%
                        'commissionAsset': 'USDT'
                    }]
                }
                
                log.info(f"🧪 TEST FUTURES ORDER executed: {order_id}, Status: FILLED, Price: {fill_price:.5f}")
                return simulated_order
            else:
                # Real Binance Futures API call
                order = binance_client.futures_create_order(
                    symbol=symbol,
                    side=side,
                    type='MARKET',
                    quantity=formatted_qty
                )
                
                log.info(f"Futures Order executed: {order['orderId']}, Status: {order['status']}")
                return order
            
        except BinanceAPIException as e:
            log.error(f"Binance Futures API error placing {side} order: {e}")
            return None
        except Exception as e:
            log.error(f"Unexpected error placing {side} futures order: {e}")
            return None
    
    def close_position(self, binance_client: BinanceClient, symbol: str, exit_price: float, exit_reason: str) -> bool:
        """
        Close current position via Binance API.
        Returns True if successful.
        """
        if self.position == 0:
            return True
        
        # Determine order side
        side = 'SELL' if self.position == 1 else 'BUY'
        
        # Place closing order
        order = self.place_market_order(binance_client, symbol, side, self.position_size)
        if order is None:
            return False
        
        # Calculate P&L and fees
        actual_exit_price = float(order.get('fills', [{}])[0].get('price', exit_price))
        pnl = self.position_size * (actual_exit_price - self.entry_price) * self.position
        exit_fee = abs(self.position_size * actual_exit_price * self.fee_perc)
        net_pnl = pnl - self.entry_fee - exit_fee
        
        # Update equity
        self.equity_total += pnl - exit_fee
        
        # Daily risk tracking (EXACTLY as in simulate_trading.py)
        initial_risk = abs(self.entry_price - self.current_sl_price)
        r_mult = net_pnl / (initial_risk * self.position_size) if initial_risk > 1e-9 else 0
        self.daily_risk_used += r_mult
        self.daily_pnl += net_pnl
        
        # Log trade
        trade_record = {
            'entry_time': self.entry_time,
            'exit_time': datetime.now(timezone.utc),
            'direction': 'Long' if self.position == 1 else 'Short',
            'size': self.position_size,
            'entry_price': self.entry_price,
            'exit_price': actual_exit_price,
            'pnl': net_pnl,
            'exit_reason': exit_reason,
            'entry_fee': self.entry_fee,
            'exit_fee': exit_fee
        }
        self.trades.append(trade_record)
        
        log.info(f"EXIT {self.position_size:.4f} {'LONG' if self.position == 1 else 'SHORT'} @ {actual_exit_price:.5f} (R: {exit_reason})")
        log.info(f"PnL: {net_pnl:.2f}, Fee: {exit_fee:.4f}, EQ: ${self.equity_total:.2f}")
        
        # Set cooldown after SL (as in ScalpingEnv)
        if exit_reason.startswith("SL"):
            self.cooldown_counter = 60  # 60 second cooldown after stop loss
            log.info(f"⏱️ SL hit, starting cooldown: {self.cooldown_counter}s")
        elif exit_reason == "TP":
            self.cooldown_counter = 3  # Short cooldown after TP
        
        # Reset position state
        self.position = 0
        self.position_size = 0.0
        self.entry_price = 0.0
        self.entry_time = None
        self.entry_fee = 0.0
        self.current_sl_price = 0.0
        self.original_sl_price = 0.0  # Reset original SL
        self.current_tp_price = 0.0
        self.peak_price_in_trade = 0.0
        self.trailing_active = False
        self.entry_atr = 0.0
        self.open_idx_sim = -1
        
        return True
    
    def open_position(self, binance_client: BinanceClient, symbol: str, direction: int, 
                     entry_price: float, signal_strength: float, current_atr: float) -> bool:
        """
        Open new position via Binance API.
        Returns True if successful.
        """
        # Calculate SL and TP (EXACTLY as in simulate_trading.py)
        sl_dist_perc_points = max(entry_price * self.min_sl_dist_perc, entry_price * 0.0005)
        sl_dist_atr_points = 0.0
        
        is_atr_valid = current_atr > 0 and current_atr >= entry_price * self.min_atr_perc_price_threshold
        if is_atr_valid:
            sl_dist_atr_points = current_atr * self.min_sl_dist_atr_mult
        
        min_sl_dist_points = max(sl_dist_perc_points, sl_dist_atr_points) if is_atr_valid else sl_dist_perc_points
        
        if min_sl_dist_points <= 1e-9:
            log.warning(f"Zero final SL distance for {'LONG' if direction == 1 else 'SHORT'}. Skipping.")
            return False
        
        # Calculate position size
        size_in_units = self.calculate_position_size(entry_price, min_sl_dist_points, signal_strength)
        
        if size_in_units <= 1e-9:
            log.warning(f"Zero position size for {'LONG' if direction == 1 else 'SHORT'}. Skipping.")
            return False
        
        # Calculate SL and TP prices
        sl_price = entry_price - min_sl_dist_points if direction == 1 else entry_price + min_sl_dist_points
        
        # TP calculation EXACTLY as in ScalpingEnv  
        # Note: a_tp is from agent action, but we don't have it here, so use default
        a_tp = 0.0  # Default when not from agent action
        tp_raw = self.rr_target + a_tp
        tp_raw = max(tp_raw, 1.0)
        tp_raw = np.clip(tp_raw, 1.0, 4.0)  # CRITICAL: Clip between 1.0-4.0 R like in env
        tp_dist_points = min_sl_dist_points * tp_raw
        tp_price = entry_price + tp_dist_points if direction == 1 else entry_price - tp_dist_points
        
        # Check equity for fees
        entry_fee = size_in_units * entry_price * self.fee_perc
        if self.equity_total - entry_fee <= 0:
            log.warning(f"Insufficient equity ({self.equity_total:.2f}) for fee ({entry_fee:.2f})")
            return False
        
        # Place order
        side = 'BUY' if direction == 1 else 'SELL'
        order = self.place_market_order(binance_client, symbol, side, size_in_units)
        if order is None:
            return False
        
        # Get actual fill price
        actual_entry_price = float(order.get('fills', [{}])[0].get('price', entry_price))
        
        # Update state
        self.equity_total -= entry_fee
        self.position = direction
        self.position_size = size_in_units
        self.entry_price = actual_entry_price
        self.current_sl_price = sl_price
        self.original_sl_price = sl_price  # Store original SL for R-multiple calculation
        self.current_tp_price = tp_price
        self.peak_price_in_trade = actual_entry_price
        self.entry_time = datetime.now(timezone.utc)
        self.entry_fee = entry_fee
        self.entry_atr = current_atr
        self.trailing_active = False
        self.open_idx_sim = int(time.time())  # Use timestamp as proxy for index
        
        # Increment daily trade count (as in ScalpingEnv)
        self.daily_trade_count += 1
        
        log.info(f"ENTER {'LONG' if direction == 1 else 'SHORT'} {size_in_units:.4f} @ {actual_entry_price:.5f}")
        log.info(f"SL: {sl_price:.5f}, TP: {tp_price:.5f}, Fee: {entry_fee:.4f}, EQ: ${self.equity_total:.2f}")
        
        return True
    
    def execute_with_binance(self, binance_client: BinanceClient, action: np.ndarray,
                           current_price: float, current_atr: float) -> None:
        """
        Main execution method called from live_trading.py.
        Processes agent actions and executes trades via Binance API.
        """
        current_time = datetime.now(timezone.utc)
        
        # Update cooldown
        self.update_cooldown()
        
        # Check daily reset and limits
        self.check_daily_reset(current_time)
        if self.check_daily_limits(current_time, current_atr):
            return  # Trading suspended due to daily limits
        
        # Skip if in cooldown
        if self.cooldown_counter > 0:
            log.info(f"🕐 COOLDOWN ACTIVE: {self.cooldown_counter}s remaining")
            return
        
        # Get symbol from config
        symbol = self.config.get('dataProvider', {}).get('symbol', 'XRPUSDT')
        if symbol.startswith('BINANCEFTS_PERP_'):
            # Extract futures symbol from CoinAPI format: BINANCEFTS_PERP_XRP_USDT -> XRPUSDT
            parts = symbol.replace('BINANCEFTS_PERP_', '').split('_')
            symbol = ''.join(parts) if len(parts) >= 2 else symbol
            log.info(f"Converted CoinAPI symbol to Binance Futures: {self.config.get('dataProvider', {}).get('symbol')} -> {symbol}")
        
        # Check SL/TP triggers first
        exit_reason, exit_price = self.check_sl_tp_triggers(current_price)
        if exit_reason:
            self.close_position(binance_client, symbol, exit_price, exit_reason)
            return
        
        # Check TSL
        tsl_exit = self.check_trailing_stop(current_price, current_atr)
        if tsl_exit:
            self.close_position(binance_client, symbol, current_price, tsl_exit)
            return
        
        # Process agent actions
        if not isinstance(action, np.ndarray) or action.size < 4:
            log.warning(f"Invalid action format: {action}")
            return
        
        action_clipped = np.clip(action.flatten(), -1.0, 1.0)
        raw_entry_sig = action_clipped[0]
        a_tp = action_clipped[2]  # Used for TP calculation
        exit_sig = action_clipped[3]

        # Apply EMA smoothing to entry signal
        if self.action_ema is None:
            self.action_ema = raw_entry_sig
        else:
            self.action_ema = self.ema_alpha * raw_entry_sig + (1 - self.ema_alpha) * self.action_ema

        # Use smoothed signal for decisions
        entry_sig = self.action_ema
        log.debug(f"📊 Signal smoothing: raw={raw_entry_sig:.6f} → smoothed={entry_sig:.6f}")
        
        # Handle exit signals (only if agent exits are enabled)
        if self.position != 0 and self.agent_exits_enabled:
            exit_sig_long_triggered = (exit_sig > self.exit_thr and self.position == 1)
            exit_sig_short_triggered = (exit_sig < -self.exit_thr and self.position == -1)
            
            if exit_sig_long_triggered or exit_sig_short_triggered:
                # Soft TP logic (as in ScalpingEnv)
                soft_tp_triggered = False
                if current_atr > 0:
                    profit = (current_price - self.entry_price) * self.position
                    atr_threshold = 1.5 * current_atr
                    if profit > atr_threshold:
                        soft_tp_triggered = True
                        log.info(f"💰 Soft TP triggered, profit: {profit:.5f} > {atr_threshold:.5f}")
                
                # Check minimum time in trade (unless soft TP)
                time_in_trade = int(time.time()) - self.open_idx_sim
                min_time_required = 30  # 30 seconds
                
                if soft_tp_triggered or time_in_trade >= min_time_required:
                    is_profit = (current_price - self.entry_price) * self.position > 0
                    exit_reason = "SOFT_TP" if soft_tp_triggered else ("TP_SIG" if is_profit else "SL_SIG")
                    self.close_position(binance_client, symbol, current_price, exit_reason)
                    return
        elif self.position != 0 and not self.agent_exits_enabled:
            # Log that agent exit signals are disabled
            log.debug(f"🚫 Agent exit signals disabled (exitAction={exit_sig:.6f}), only SL/TP will close trades")
        
        # Handle entry signals (only if flat and trading allowed)
        if self.position == 0 and self.trading_allowed:
            # Time-of-day blackout check (as in ScalpingEnv)
            current_hour = current_time.hour
            if current_hour in self.DISALLOWED_HOURS:
                log.info(f"🚫 ENTRY BLOCKED: Blackout hour {current_hour} UTC")
                return  # Skip entry during blackout hours

            # Decision throttling - only evaluate entries at intervals
            current_timestamp = time.time()
            if current_timestamp - self.last_decision_time < self.decision_interval:
                log.debug(f"🕐 ENTRY THROTTLED: {self.decision_interval - (current_timestamp - self.last_decision_time):.1f}s remaining")
                return  # Skip evaluation during throttle period

            self.last_decision_time = current_timestamp
            
            # 🚨 ACTION STABILITY CHECK for 1s decisions
            self.recent_actions.append(entry_sig)
            if len(self.recent_actions) > self.action_stability_window:
                self.recent_actions = self.recent_actions[-self.action_stability_window:]
            
            # Check if we have enough history and if actions are stable
            action_stable = True
            if len(self.recent_actions) >= self.action_stability_window:
                action_std = np.std(self.recent_actions)
                action_range = max(self.recent_actions) - min(self.recent_actions)
                
                if action_std > self.action_stability_threshold or action_range > 1.2:
                    action_stable = False
                    log.info(f"🔍 ACTION UNSTABLE: std={action_std:.3f}, range={action_range:.3f}, recent={self.recent_actions}")
            
            triggered_pos = 0
            log.info(f"🎯 ENTRY CHECK: entry_sig={entry_sig:.6f}, long_thr={self.long_entry_thr}, short_thr={self.short_entry_thr}, stable={action_stable}")
            
            if action_stable:  # Only proceed if actions are stable
                if entry_sig > self.long_entry_thr:
                    triggered_pos = 1
                    log.info(f"🟢 LONG ENTRY TRIGGERED: {entry_sig:.6f} > {self.long_entry_thr} (STABLE)")
                elif entry_sig < -self.short_entry_thr:
                    triggered_pos = -1
                    log.info(f"🔴 SHORT ENTRY TRIGGERED: {entry_sig:.6f} < {-self.short_entry_thr} (STABLE)")
            else:
                log.info(f"⚠️ ENTRY BLOCKED: Action unstable - {entry_sig:.6f} (std={action_std:.3f}, range={action_range:.3f})")
            
            if triggered_pos != 0:
                signal_strength = abs(entry_sig)
                success = self.open_position(binance_client, symbol, triggered_pos,
                                           current_price, signal_strength, current_atr)
                if success:
                    # Update TP calculation with actual a_tp from agent
                    tp_raw = self.rr_target + a_tp
                    tp_raw = max(tp_raw, 1.0)
                    tp_raw = np.clip(tp_raw, 1.0, 4.0)  # CRITICAL: Clip like in env
                    sl_dist = abs(self.entry_price - self.current_sl_price)
                    tp_dist = sl_dist * tp_raw
                    self.current_tp_price = (self.entry_price + tp_dist if self.position == 1
                                           else self.entry_price - tp_dist)
                    log.info(f"Updated TP with agent a_tp={a_tp:.3f}: {self.current_tp_price:.5f}")
                else:
                    # 🚨 CRITICAL FIX: Set cooldown after failed order attempt
                    self.cooldown_counter = 30  # 30 second cooldown after failed order
                    log.warning(f"❌ ORDER FAILED: Starting {self.cooldown_counter}s cooldown to prevent rapid retries")